package com.fshows.langchain4jexamples.controller;

import com.alibaba.fastjson.JSON;
import com.fshows.langchain4jexamples.agent.RagAgent;
import com.fshows.langchain4jexamples.dto.QueryRequest;
import com.fshows.langchain4jexamples.dto.VectorizeRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * RAG Controller - 提供RAG相关的REST API接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/rag")
@CrossOrigin(origins = "*")
@Slf4j
public class RagController {

    @Autowired
    private RagAgent ragAgent;

    /**
     * 向量化文件接口
     * @param request 向量化请求
     * @return 向量化结果
     */
    @PostMapping("/vectorize")
    public ResponseEntity<RagAgent.VectorizeResult> vectorizeFile(@RequestBody VectorizeRequest request) {
        log.info("收到向量化请求: {}", JSON.toJSONString(request));
        try {
            // 参数验证
            if (request.getFilePath() == null || request.getFilePath().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    new RagAgent.VectorizeResult(false, "文件路径不能为空", 0, null)
                );
            }

            // 设置默认值
            int chunkSize = request.getChunkSize() > 0 ? request.getChunkSize() : 1024;
            int chunkOverlap = request.getChunkOverlap() >= 0 ? request.getChunkOverlap() : 50;

            // 执行向量化
            RagAgent.VectorizeResult result = ragAgent.vectorizeFile(
                request.getFilePath(), 
                chunkSize, 
                chunkOverlap
            );

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(
                new RagAgent.VectorizeResult(false, "服务器内部错误: " + e.getMessage(), 0, null)
            );
        }
    }

    /**
     * 知识库查询接口
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/query")
    public ResponseEntity<RagAgent.QueryResult> queryKnowledgeBase(@RequestBody QueryRequest request) {
        log.info("收到查询请求: {}", JSON.toJSONString(request));
        try {
            // 参数验证
            if (request.getQuery() == null || request.getQuery().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(
                    new RagAgent.QueryResult(false, "查询内容不能为空", "", null, null)
                );
            }

            // 设置默认值
            int maxResults = request.getMaxResults() > 0 ? request.getMaxResults() : 5;
            double minScore = request.getMinScore() >= 0 ? request.getMinScore() : 0.7;

            // 执行查询
            RagAgent.QueryResult result = ragAgent.queryKnowledgeBase(
                request.getQuery().trim(), 
                maxResults, 
                minScore
            );
            ResponseEntity<RagAgent.QueryResult> response = ResponseEntity.ok(result);
            return response;
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body(
                new RagAgent.QueryResult(false, "服务器内部错误: " + e.getMessage(), 
                    request.getQuery(), null, null)
            );
        }
    }

    /**
     * 健康检查接口
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("RAG服务运行正常");
    }
}
