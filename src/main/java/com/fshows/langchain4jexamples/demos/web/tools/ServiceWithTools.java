package com.fshows.langchain4jexamples.demos.web.tools;


import dev.langchain4j.agent.tool.Tool;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.AiServices;

/**
 * ServiceWithTools
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/11
 */
public class ServiceWithTools {

    static class Calculator {

        @Tool("计算字符串的长度")
        int stringLength(String s) {
            System.out.println("计算字符串的长度 s='" + s + "'");
            return s.length();
        }

        @Tool("计算两个数之和")
        int add(int a, int b) {
            System.out.println("两数相加结果: a=" + a + ", b=" + b);
            return a + b;
        }

        @Tool("计算一个数的平方根")
        double sqrt(int x) {
            System.out.println("求平方根: x=" + x);
            return Math.sqrt(x);
        }
    }

    interface Assistant {
        String chat(String userMessage);
    }

    public static void main(String[] args) {
        ChatModel model = OpenAiChatModel.builder()
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                .apiKey(ApiKeys.OPENAI_API_KEY)
                .modelName("qwen-max")
                .strictTools(true)
                .build();

        Assistant assistant = AiServices.builder(Assistant.class)
                .chatModel(model)
                .tools(new Calculator())
                .chatMemory(MessageWindowChatMemory.withMaxMessages(10))
                .build();

        String question = "'hello'和'world'两个词的字母个数之和,然后用和再求平方根是多少?";
        String answer = assistant.chat(question);

        System.out.println(answer);
        // The square root of the sum of the number of letters in the words "hello" and "world" is approximately 3.162.
    }
}