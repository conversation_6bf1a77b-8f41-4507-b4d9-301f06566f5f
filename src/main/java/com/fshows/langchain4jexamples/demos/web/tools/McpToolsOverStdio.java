package com.fshows.langchain4jexamples.demos.web.tools;


import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.stdio.StdioMcpTransport;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.tool.ToolProvider;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * McpToolsOverStdio
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/12
 */
public class McpToolsOverStdio {
    public static final String FILE_TO_BE_READ = "D:\\file.txt";
    public static void main(String[] args) throws Exception {
        ChatModel model = OpenAiChatModel.builder()
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                .apiKey(ApiKeys.OPENAI_API_KEY)
                .modelName("qwen-max")
                .logRequests(true)
                .logResponses(true)
                .build();

        Map<String,String> env = new HashMap<>();
//        env.put("CC_URL","https://cc.chenge.ink");
//        env.put("CC_ID","72JVhp8hpuembRHUZ1NPzq");
//        env.put("CC_PASSWORD","1jTVe7RrK9eSak2ezRrL7u");
//        McpTransport transport = new StdioMcpTransport.Builder()
//                .command(List.of("C:\\Program Files\\nodejs\\node_global\\mcp-server-weread.cmd"))
//                .environment(env)
//                .logEvents(true) // 仅当你想在日志中查看流量时
//                .build();


//        https://modelscope.cn/mcp/servers/@yan5236/bing-cn-mcp-server
        McpTransport transport = new StdioMcpTransport.Builder()
                .command(List.of("C:\\Program Files\\nodejs\\npx.cmd","bing-cn-mcp"))
                .environment(env)
                .logEvents(true) // 仅当你想在日志中查看流量时
                .build();



        McpClient mcpClient = new DefaultMcpClient.Builder()
                .transport(transport)
                .build();

        ToolProvider toolProvider = McpToolProvider.builder()
                .mcpClients(List.of(mcpClient))
                .build();

        Bot bot = AiServices.builder(Bot.class)
                .chatModel(model)
                .toolProvider(toolProvider)
                .build();

        try {
            File file = new File(FILE_TO_BE_READ);
            String response = bot.chat("2024年全国GDP是多少");
            System.out.println("RESPONSE: " + response);
        } finally {
            mcpClient.close();
        }
    }
}