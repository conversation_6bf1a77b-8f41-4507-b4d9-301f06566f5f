package com.fshows.langchain4jexamples.demos.web.tools;


import dev.langchain4j.mcp.McpToolProvider;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.tool.ToolProvider;

import java.time.Duration;
import java.util.List;

/**
 * McpToolsOverHttp
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/6/12
 */
public class McpToolsOverHttp {
    public static void main(String[] args) throws Exception {
        ChatModel model = OpenAiChatModel.builder()
                .baseUrl("https://dashscope.aliyuncs.com/compatible-mode/v1")
                .apiKey(ApiKeys.OPENAI_API_KEY)
                .modelName("qwen-max")
                .logRequests(true)
                .logResponses(true)
                .build();

        // https://modelscope.cn/mcp/servers/@Joooook/12306-mcp
        McpTransport transport = new HttpMcpTransport.Builder()
                .sseUrl("https://mcp.api-inference.modelscope.net/6691a75d8d9545/sse")
                .timeout(Duration.ofSeconds(60))
                .logRequests(true)
                .logResponses(true)
                .build();

        McpClient mcpClient = new DefaultMcpClient.Builder()
                .transport(transport)
                .build();

        ToolProvider toolProvider = McpToolProvider.builder()
                .mcpClients(List.of(mcpClient))
                .build();

        Bot bot = AiServices.builder(Bot.class)
                .chatModel(model)
                .toolProvider(toolProvider)
                .build();
        try {
            String response = bot.chat("帮我查一下明天从北京到上海的火车票还有多少张");
            System.out.println(response);
        } finally {
            mcpClient.close();
        }
    }
}