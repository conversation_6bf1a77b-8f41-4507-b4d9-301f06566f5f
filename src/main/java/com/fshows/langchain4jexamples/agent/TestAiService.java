package com.fshows.langchain4jexamples.agent;

import dev.langchain4j.community.model.dashscope.QwenChatModel;
import dev.langchain4j.service.AiServices;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;

public interface TestAiService {

    @SystemMessage("你是Java技术专家，用简洁代码示例回答")
    @UserMessage("解决：{{problem}}")
    String solveProblem(@V("") String problem);

    QwenChatModel CHAT_MODEL = QwenChatModel.builder()
            .apiKey(System.getenv("ALI_AI_KEY"))
            .modelName("qwen-max")
            .build();
    TestAiService testAiService = AiServices.create(TestAiService.class,CHAT_MODEL);

}
