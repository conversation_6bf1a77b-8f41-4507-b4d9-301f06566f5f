package com.fshows.langchain4jexamples.agent;

import com.alibaba.fastjson.JSONArray;
import dev.langchain4j.community.model.dashscope.QwenChatModel;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.document.loader.FileSystemDocumentLoader;
import dev.langchain4j.data.document.splitter.DocumentSplitters;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.chroma.ChromaEmbeddingStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * RAG Agent Service - 提供文档向量化和知识库查询功能
 * <AUTHOR>
 */
@Service
public class RagAgent {

    @Value("${rag.chroma.endpoint:http://localhost:8000}")
    private String chromaEndpoint;

    @Value("${rag.chroma.collection:rag_knowledge_base}")
    private String collectionName;

    @Value("${rag.qwen.model:text-embedding-v4}")
    private String embeddingModelName;

    @Value("${rag.qwen.dimension:1024}")
    private int embeddingDimension;

    @Value("${rag.qwen.chat.model:qwen-max}")
    private String chatModelName;

    private EmbeddingStore<TextSegment> embeddingStore;
    private QwenEmbeddingModel embeddingModel;
    private QwenChatModel chatModel;

    // @Autowired
    // private TestAiService testAiService;

    /**
     * 初始化RAG组件
     */
    @PostConstruct
    public void init() {
        // 初始化Chroma向量数据库
        this.embeddingStore = ChromaEmbeddingStore.builder()
                .baseUrl(chromaEndpoint)
                .collectionName(collectionName)
                .logRequests(true)
                .logResponses(true)
                .build();

        // 初始化Qwen嵌入模型
        String aliAiKey = System.getenv("ALI_AI_KEY");
        this.embeddingModel = QwenEmbeddingModel.builder()
                .apiKey(aliAiKey)
                .modelName(embeddingModelName)
                .dimension(embeddingDimension)
                .build();

        // 初始化Qwen聊天模型
        this.chatModel = QwenChatModel.builder()
                .apiKey(aliAiKey)
                .modelName(chatModelName)
                .build();
    }

    /**
     * 向量化文件
     * @param filePath 文件路径
     * @param chunkSize 分块大小
     * @param chunkOverlap 分块重叠
     * @return 向量化结果
     */
    public VectorizeResult vectorizeFile(String filePath, int chunkSize, int chunkOverlap) {
        try {
            // 加载文档
            Document document = FileSystemDocumentLoader.loadDocument(filePath);

            // 使用递归分割器分割文档
            DocumentSplitter splitter = DocumentSplitters.recursive(chunkSize, chunkOverlap);
            List<TextSegment> segments = splitter.split(document);

            // 向量化并存储
            List<String> segmentIds = new ArrayList<>();
            for (TextSegment segment : segments) {
                Embedding embedding = embeddingModel.embed(segment).content();
                String segmentId = embeddingStore.add(embedding, segment);
                segmentIds.add(segmentId);
            }

            return new VectorizeResult(true, "文件向量化成功", segments.size(), segmentIds);
        } catch (Exception e) {
            return new VectorizeResult(false, "文件向量化失败: " + e.getMessage(), 0, null);
        }
    }

    /**
     * 大模型分段
     */
    public static void main(String[] args) throws IOException {
        // String content = new String(Files.readAllBytes(Paths.get("/Users/<USER>/Documents/rag/本地生活FAQ04.md")));
        // String content = "";
        String aliAiKey = System.getenv("ALI_AI_KEY");
        QwenChatModel chatModel = QwenChatModel.builder()
                .apiKey(aliAiKey)
                .modelName("qwen-max")
                .build();


        List<Document> documents = FileSystemDocumentLoader.loadDocumentsRecursively("/Users/<USER>/data/data/0803/账户能力");
        for (Document document : documents) {
            String text = document.text();
            String chat = chatModel.chat("我现在要做向量化   请你帮我把这个文档分割一下,输出JSON格式 [\"分段内容1\", \"分段内容2\", \"...\"] ，只输出标准 json  不要再 json 外有任何文字，文档内容如下 \n" + text);
            System.out.println(chat);
            List<String> strings = JSONArray.parseArray(chat, String.class);
            // InMemoryEmbeddingStore<TextSegment> embeddingStore = new InMemoryEmbeddingStore<>();
            String chromaEndpoint = "http://localhost:8000";
            EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
                    .builder()
                    .baseUrl(chromaEndpoint)
                    .collectionName("rag_knowledge_base")
                    .logRequests(true)
                    .logResponses(true)
                    .build();

            QwenEmbeddingModel embeddingModel = QwenEmbeddingModel.builder()
                    .apiKey(aliAiKey)
                    .modelName("text-embedding-v4")
                    .dimension(1024)
                    .build();

            for (String string : strings) {
                TextSegment segment = TextSegment.from(string);
                Embedding embedding = embeddingModel.embed(segment).content();
                embeddingStore.add(embedding, segment);
            }
        }



        System.out.println("向量化完成");

        // Embedding queryEmbedding = embeddingModel.embed("新增抖音商品，提示类目不存在").content();
        // EmbeddingSearchRequest embeddingSearchRequest = EmbeddingSearchRequest.builder()
        //         .queryEmbedding(queryEmbedding)
        //         .maxResults(1)
        //         .minScore(0.8)
        //         .build();
        //
        // List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(embeddingSearchRequest).matches();
        // EmbeddingMatch<TextSegment> embeddingMatch = matches.get(0);
        // System.out.println(embeddingMatch.score());
        // System.out.println(embeddingMatch.embedded().text());

    }
    /**
     * 向量化结果类
     */
    public static class VectorizeResult {
        private boolean success;
        private String message;
        private int segmentCount;
        private List<String> segmentIds;

        public VectorizeResult(boolean success, String message, int segmentCount, List<String> segmentIds) {
            this.success = success;
            this.message = message;
            this.segmentCount = segmentCount;
            this.segmentIds = segmentIds;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public int getSegmentCount() { return segmentCount; }
        public void setSegmentCount(int segmentCount) { this.segmentCount = segmentCount; }
        public List<String> getSegmentIds() { return segmentIds; }
        public void setSegmentIds(List<String> segmentIds) { this.segmentIds = segmentIds; }
    }

    /**
     * 查询结果类
     */
    public static class QueryResult {
        private boolean success;
        private String message;
        private String query;
        private String answer;
        private List<MatchResult> matches;

        public QueryResult(boolean success, String message, String query, String answer, List<MatchResult> matches) {
            this.success = success;
            this.message = message;
            this.query = query;
            this.answer = answer;
            this.matches = matches;
        }

        // Getters and setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public String getQuery() { return query; }
        public void setQuery(String query) { this.query = query; }
        public String getAnswer() { return answer; }
        public void setAnswer(String answer) { this.answer = answer; }
        public List<MatchResult> getMatches() { return matches; }
        public void setMatches(List<MatchResult> matches) { this.matches = matches; }
    }

    /**
     * 匹配结果类
     */
    public static class MatchResult {
        private String text;
        private double score;

        public MatchResult(String text, double score) {
            this.text = text;
            this.score = score;
        }

        // Getters and setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public double getScore() { return score; }
        public void setScore(double score) { this.score = score; }
    }

    /*
     * 以下是原有的示例代码，已注释保留作为参考
     */


    /**
     * 内存向量库
     * 文档-手动分割模式
     * LLM Qwen
     */
    // public static void main(String[] args) {
    //     // 向量数据库（内存版）
    //     InMemoryEmbeddingStore<TextSegment> embeddingStore = new InMemoryEmbeddingStore<>();
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //     QwenEmbeddingModel  embeddingModel= QwenEmbeddingModel.builder()
    //             .apiKey(aliAiKey)
    //             .build();
    //
    //     // 利用向量模型进行向量化， 然后存储向量到向量数据库
    //     TextSegment segment1 = TextSegment.from("""
    //             来团呗oem代理商手机号修改
    //                                                    1. 业务提供老手机号A、新手机号B
    //                                                    2. 确定新手机号B，在系统中不存在，在ailike_b_oem_user查询验证，若存在不允许订正
    //                                                    SELECT * FROM `ailike_b_oem_user` WHERE `phone_number` = 'kLErL6sFJl865ZAgbzxoMQ==';
    //                                                    3. 老手机号在ailike_b_oem_user表，查询user_id，在ailike_b_oem_identity确定数据；
    //                                                      a. 若单条，直接完成下面订正
    //                                                      b. 若多条，通过belong_id确定要修改的代理商
    //                                                    UPDATE `ailike_b_oem_agent` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
    //                                                    UPDATE `ailike_b_oem_user` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
    //             """);
    //     Embedding embedding1 = embeddingModel.embed(segment1).content();
    //     embeddingStore.add(embedding1, segment1);
    //
    //     // 利用向量模型进行向量化， 然后存储向量到向量数据库
    //     TextSegment segment2 = TextSegment.from("""
    //             来团呗商家合成上限
    //                              原订正：
    //                              UPDATE `ailike_b_merchant_video_config` SET `total_limit` = '10000' WHERE `id` = 25120;
    //                              新订正：
    //                              apiFox——项目：抖音本地生活
    //                              /material-group/update-merchant-video-config
    //                              {
    //                                "merchantId": "商户id",
    //                                "addCount": 10000  //增加次数
    //                              }
    //             """);
    //     Embedding embedding2 = embeddingModel.embed(segment2).content();
    //     embeddingStore.add(embedding2, segment2);
    //
    //     // 利用向量模型进行向量化， 然后存储向量到向量数据库
    //     TextSegment segment3 = TextSegment.from("""
    //             来团呗，团小贝发送消息：非常抱歉，获取回答时出现了问题，请稍后重试
    //                                                                                                                        现象：
    //                                                                                                                        1. 全部商户均无法正常使用团小贝
    //                                                                                                                        2. 团小贝发送消息：非常抱歉，获取回答时出现了问题，请稍后重试
    //                                                                                                                        原因：
    //                                                                                                                        1. 全部商户均无法正常使用，账号资金用完了
    //                                                                                                                        2. 接口响应超时了
    //                                                                                                                        解决方案：
    //                                                                                                                        1. 全局商户，均无法使用，群里ding值班人员，由技术人员切换资金账号
    //                                                                                                                        2. 接口超时，问题需要的回答字数和数量过多，建议减少问题字数和数量，或者是总接口账号次数用尽，用一下模版回答，会好一点
    //                                                                                                                        3.  超时了，等30min后，使用模版提问/换个提问方式，部分问题ai暂不支持回答 \s
    //             """);
    //     Embedding embedding3 = embeddingModel.embed(segment3).content();
    //     embeddingStore.add(embedding3, segment3);
    //
    //
    //     // 需要查询的内容 向量化
    //     Embedding queryEmbedding = embeddingModel.embed("团小贝用不了").content();
    //
    //     // 去向量数据库查询
    //     // 构建查询条件
    //     EmbeddingSearchRequest build = EmbeddingSearchRequest.builder()
    //             .queryEmbedding(queryEmbedding)
    //             .maxResults(1)
    //             .build();
    //
    //
    //     // 查询
    //     EmbeddingSearchResult<TextSegment> segmentEmbeddingSearchResult = embeddingStore.search(build);
    //     segmentEmbeddingSearchResult.matches().forEach(embeddingMatch -> {
    //         System.out.println(embeddingMatch.score());
    //         System.out.println(embeddingMatch.embedded().text());
    //     });
    // }

    /**
     *  查看向量化大小
     */
    // public static void main(String[] args) {
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //     QwenEmbeddingModel  embeddingModel= QwenEmbeddingModel.builder()
    //             .apiKey(aliAiKey)
    //             .modelName("text-embedding-v4")
    //             .dimension(1024)
    //             .build();
    //     Embedding queryEmbedding = embeddingModel.embed("团小贝用不了").content();
    //     DisabledEmbeddingModel disabledEmbeddingModel = new DisabledEmbeddingModel();
    //
    //     System.out.println(queryEmbedding);
    //     System.out.println(queryEmbedding.vector().length);
    //
    // }

    /**
     *  调用 ai service
     */
    // public static void main(String[] args) {
    //     // QwenChatModel chatModel = QwenChatModel.builder()
    //     //         .apiKey("sk-e461c98bf4cb4156b8eb47fa6b9fd943")
    //     //         .modelName("qwen-max")
    //     //         .build();
    //     // TestAiService testAiService = AiServices.create(TestAiService.class,chatModel);
    //     // String result = testAiService.solveProblem("你能做什么");
    //     String result = TestAiService.testAiService.solveProblem("你能做什么");
    //     System.out.println(result);
    // }

//    /**
//     *  调用 mcp
//     */
//     public static void main(String[] args) {
//             QwenChatModel chatModel = QwenChatModel.builder()
//                     .apiKey("sk-e461c98bf4cb4156b8eb47fa6b9fd943")
//                     .modelName("qwen-max")
//                     .build();
//
//
//         // ChatModel model = OpenAiChatModel.builder()
//         //         .apiKey(System.getenv("OPENAI_API_KEY"))
//         //         .modelName("gpt-4o-mini")
//         //         .logRequests(true)
//         //         .logResponses(true)
//         //         .build();
//
//         McpTransport transport = new HttpMcpTransport.Builder()
//                 .sseUrl("https://mcp.api-inference.modelscope.net/6d3773f6a2c54e/sse")
//                 .timeout(Duration.ofSeconds(200))
//                 .logRequests(true)
//                 .logResponses(true)
//                 .build();
//
//         McpClient mcpClient = new DefaultMcpClient.Builder()
//                 .transport(transport)
//                 .build();
//
//         ToolProvider toolProvider = McpToolProvider.builder()
//                 .mcpClients(mcpClient)
//                 .build();
//
//         Bot bot = AiServices.builder(Bot.class)
//                 .chatModel(chatModel)
//                 .toolProvider(toolProvider)
//                 .build();
//         String response = bot.chat("通过工具查询杭州车站信息的列表");
//         System.out.println(response);
//
//     }

    /**
     *  调用 mcp2
     */
//    public static void main(String[] args) {
//        QwenChatModel chatModel = QwenChatModel.builder()
//                .apiKey("sk-e461c98bf4cb4156b8eb47fa6b9fd943")
//                .modelName("qwen-max")
//                .build();
//
//
//        // ChatModel model = OpenAiChatModel.builder()
//        //         .apiKey(System.getenv("OPENAI_API_KEY"))
//        //         .modelName("gpt-4o-mini")
//        //         .logRequests(true)
//        //         .logResponses(true)
//        //         .build();
//
//        McpTransport transport = new HttpMcpTransport.Builder()
//                .sseUrl("https://mcp.api-inference.modelscope.net/48711927ae4f4c/sse")
//                .timeout(Duration.ofSeconds(200))
//                .logRequests(true)
//                .logResponses(true)
//                .build();
//
//        McpClient mcpClient = new DefaultMcpClient.Builder()
//                .transport(transport)
//                .build();
//
//        ToolProvider toolProvider = McpToolProvider.builder()
//                .mcpClients(mcpClient)
//                .build();
//
//        Bot bot = AiServices.builder(Bot.class)
//                .chatModel(chatModel)
//                .toolProvider(toolProvider)
//                .build();
//        String response = bot.chat("帮我执行下这个用例  {\n" +
//                "            \"name\": \"成功登录\",\n" +
//                "            \"description\": \"使用正确的账号和密码登录系统\",\n" +
//                "            \"steps\": [\n" +
//                "                {\n" +
//                "                    \"action\": \"打开登录页面 https://b.51fubei.com/login\",\n" +
//                "                    \"expected\": \"登录页面成功加载\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"action\": \"在账号输入框中输入 'A86556\",\n" +
//                "                    \"expected\": \"账号输入框中显示 'A86556'\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"action\": \"在密码输入框中输入 'Aa111111'\",\n" +
//                "                    \"expected\": \"密码输入框中显示 'Aa111111'\"\n" +
//                "                },\n" +
//                "                {\n" +
//                "                    \"action\": \"点击登录按钮\",\n" +
//                "                    \"expected\": \"成功登录系统，跳转到主页或用户仪表盘\"\n" +
//                "                }\n" +
//                "            ]\n" +
//                "        }   并帮我输出一份测试报告");
//        System.out.println("------------------------------------------------------");
//        System.out.println(response);
//
//    }


    // public static void main(String[] args) {
    //     QwenChatModel chatModel = QwenChatModel.builder()
    //             .apiKey("sk-e461c98bf4cb4156b8eb47fa6b9fd943")
    //             .modelName("qwen-max")
    //             .build();
    //
    //
    //     // ChatModel model = OpenAiChatModel.builder()
    //     //         .apiKey(System.getenv("OPENAI_API_KEY"))
    //     //         .modelName("gpt-4o-mini")
    //     //         .logRequests(true)
    //     //         .logResponses(true)
    //     //         .build();
    //
    //     McpTransport transport = new HttpMcpTransport.Builder()
    //             .sseUrl("http://localhost:8931/sse")
    //             .timeout(Duration.ofSeconds(200))
    //             .logRequests(true)
    //             .logResponses(true)
    //             .build();
    //
    //     McpClient mcpClient = new DefaultMcpClient.Builder()
    //             .transport(transport)
    //             .build();
    //
    //     ToolProvider toolProvider = McpToolProvider.builder()
    //             .mcpClients(mcpClient)
    //             .build();
    //
    //     Bot bot = AiServices.builder(Bot.class)
    //             .chatModel(chatModel)
    //             .toolProvider(toolProvider)
    //             .build();
    //     String response = bot.chat("帮我执行下这个用例  {\n" +
    //             "            \"name\": \"成功登录\",\n" +
    //             "            \"description\": \"使用正确的账号和密码登录系统\",\n" +
    //             "            \"steps\": [\n" +
    //             "                {\n" +
    //             "                    \"action\": \"打开登录页面 https://b.51fubei.com/login\",\n" +
    //             "                    \"expected\": \"登录页面成功加载\"\n" +
    //             "                },\n" +
    //             "                {\n" +
    //             "                    \"action\": \"在账号输入框中输入 'A86556\",\n" +
    //             "                    \"expected\": \"账号输入框中显示 'A86556'\"\n" +
    //             "                },\n" +
    //             "                {\n" +
    //             "                    \"action\": \"在密码输入框中输入 'Aa111111'\",\n" +
    //             "                    \"expected\": \"密码输入框中显示 'Aa111111'\"\n" +
    //             "                },\n" +
    //             "                {\n" +
    //             "                    \"action\": \"点击登录按钮\",\n" +
    //             "                    \"expected\": \"成功登录系统，跳转到主页或用户仪表盘\"\n" +
    //             "                }\n" +
    //             "            ]\n" +
    //             "        }   并帮我输出一份测试报告");
    //     System.out.println("------------------------------------------------------");
    //     System.out.println(response);
    //
    // }


    // public static void main(String[] args) {
    //     try (MilvusContainer milvus = new MilvusContainer("milvusdb/milvus:v2.3.1")) {
    //         milvus.start();
    //         EmbeddingStore<TextSegment> embeddingStore = MilvusEmbeddingStore.builder()
    //                 .uri(milvus.getEndpoint())
    //                 .collectionName("test_collection")
    //                 .dimension(384)
    //                 .build();
    //
    //         EmbeddingModel embeddingModel = new AllMiniLmL6V2EmbeddingModel();
    //
    //         TextSegment segment1 = TextSegment.from("I like football.");
    //         Embedding embedding1 = embeddingModel.embed(segment1).content();
    //         embeddingStore.add(embedding1, segment1);
    //
    //         TextSegment segment2 = TextSegment.from("The weather is good today.");
    //         Embedding embedding2 = embeddingModel.embed(segment2).content();
    //         embeddingStore.add(embedding2, segment2);
    //
    //         Embedding queryEmbedding = embeddingModel.embed("What is your favourite sport?").content();
    //         EmbeddingSearchRequest embeddingSearchRequest = EmbeddingSearchRequest.builder()
    //                 .queryEmbedding(queryEmbedding)
    //                 .maxResults(1)
    //                 .build();
    //         List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(embeddingSearchRequest).matches();
    //         EmbeddingMatch<TextSegment> embeddingMatch = matches.get(0);
    //
    //         System.out.println(embeddingMatch.score()); // 0.8144287765026093
    //         System.out.println(embeddingMatch.embedded().text()); // I like football.
    //     }
    // }


    // public static void main(String[] args) {
    //     try (ChromaDBContainer chroma = new ChromaDBContainer("chromadb/chroma:0.5.4")) {
    //         chroma.start();
    //
    //         EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    //                 .builder()
    //                 .baseUrl(chroma.getEndpoint())
    //                 .collectionName(randomUUID())
    //                 .logRequests(true)
    //                 .logResponses(true)
    //                 .build();
    //
    //         String endpoint = chroma.getEndpoint();
    //
    //         // EmbeddingModel embeddingModel = new AllMiniLmL6V2EmbeddingModel();
    //         String aliAiKey = System.getenv("ALI_AI_KEY");
    //         QwenEmbeddingModel  embeddingModel= QwenEmbeddingModel.builder()
    //                 .apiKey(aliAiKey)
    //                 .modelName("text-embedding-v4")
    //                 .dimension(1024)
    //                 .build();
    //
    //         TextSegment segment1 = TextSegment.from("I like football.");
    //         Embedding embedding1 = embeddingModel.embed(segment1).content();
    //         embeddingStore.add(embedding1, segment1);
    //
    //         TextSegment segment2 = TextSegment.from("The weather is good today.");
    //         Embedding embedding2 = embeddingModel.embed(segment2).content();
    //         embeddingStore.add(embedding2, segment2);
    //
    //         Embedding queryEmbedding = embeddingModel.embed("What is your favourite sport?").content();
    //         EmbeddingSearchRequest embeddingSearchRequest = EmbeddingSearchRequest.builder()
    //                 .queryEmbedding(queryEmbedding)
    //                 .maxResults(1)
    //                 .build();
    //         List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(embeddingSearchRequest).matches();
    //         EmbeddingMatch<TextSegment> embeddingMatch = matches.get(0);
    //
    //         System.out.println(embeddingMatch.score()); // 0.8144288493114709
    //         System.out.println(embeddingMatch.embedded().text()); // I like football.
    //
    //         chroma.stop();
    //     }catch (Exception e){
    //         throw e;
    //     }
    // }


    /**
     * 使用向量数据库 Chroma
     */
    // public static void main(String[] args) {
    //     String chromaEndpoint = "http://localhost:8000";
    //     EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    //             .builder()
    //             .baseUrl(chromaEndpoint)
    //             .collectionName("test1_collection")
    //             .logRequests(true)
    //             .logResponses(true)
    //             .build();
    //
    //     // EmbeddingModel embeddingModel = new AllMiniLmL6V2EmbeddingModel();
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //             QwenEmbeddingModel  embeddingModel= QwenEmbeddingModel.builder()
    //                     .apiKey(aliAiKey)
    //                     .modelName("text-embedding-v4")
    //                     .dimension(1024)
    //                     .build();
    //
    //             TextSegment segment1 = TextSegment.from("I like football.");
    //             Embedding embedding1 = embeddingModel.embed(segment1).content();
    //             embeddingStore.add(embedding1, segment1);
    //
    //             TextSegment segment2 = TextSegment.from("The weather is good today.");
    //             Embedding embedding2 = embeddingModel.embed(segment2).content();
    //             embeddingStore.add(embedding2, segment2);
    //
    //             Embedding queryEmbedding = embeddingModel.embed("What is your favourite sport?").content();
    //             EmbeddingSearchRequest embeddingSearchRequest = EmbeddingSearchRequest.builder()
    //                     .queryEmbedding(queryEmbedding)
    //                     .maxResults(1)
    //                     .build();
    //             List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(embeddingSearchRequest).matches();
    //             EmbeddingMatch<TextSegment> embeddingMatch = matches.get(0);
    //
    //             System.out.println(embeddingMatch.score()); // 0.8144288493114709
    //             System.out.println(embeddingMatch.embedded().text()); // I like football.
    //
    // }


    /**
     * Chroma库中查询
     */
    // public static void main(String[] args) {
    //     String chromaEndpoint = "http://localhost:8000";
    //     EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    //             .builder()
    //             .baseUrl(chromaEndpoint)
    //             .collectionName("test1_collection")
    //             .logRequests(true)
    //             .logResponses(true)
    //             .build();
    //
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //     QwenEmbeddingModel embeddingModel = QwenEmbeddingModel.builder()
    //             .apiKey(aliAiKey)
    //             .modelName("text-embedding-v4")
    //             .dimension(1024)
    //             .build();
    //
    //     Embedding queryEmbedding = embeddingModel.embed("闭环商户的闭环商品的商品状态和来客后台的不一致").content();
    //     EmbeddingSearchRequest embeddingSearchRequest = EmbeddingSearchRequest.builder()
    //             .queryEmbedding(queryEmbedding)
    //             .maxResults(1)
    //             .minScore(0.8)
    //             .build();
    //
    //     List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(embeddingSearchRequest).matches();
    //     EmbeddingMatch<TextSegment> embeddingMatch = matches.get(0);
    //     System.out.println(embeddingMatch.score());
    //     System.out.println(embeddingMatch.embedded().text());
    //
    // }

    /**
     * 向量化文档
     */
    // public static void main(String[] args) {
    //     String chromaEndpoint = "http://localhost:8000";
    //     EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    //             .builder()
    //             .baseUrl(chromaEndpoint)
    //             .collectionName("test1_collection")
    //             .logRequests(true)
    //             .logResponses(true)
    //             .build();
    //
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //     QwenEmbeddingModel embeddingModel = QwenEmbeddingModel.builder()
    //             .apiKey(aliAiKey)
    //             .modelName("text-embedding-v4")
    //             .dimension(1024)
    //             .build();
    //
    //     List<Document> documents = FileSystemDocumentLoader.loadDocuments("/Users/<USER>/Documents/rag");
    //     // for (Document document : documents) {
    //     //     TextSegment segment = TextSegment.from(document.text());
    //     //     Embedding embedding = embeddingModel.embed(segment).content();
    //     //     embeddingStore.add(embedding, segment);
    //     // }
    //
    //     // 定义标题分割正则
    //     // String titleRegex = "(?=\\n#{1,6} )";
    //     String titleRegex = "(?=\\r?\\n#{3}\\s+)";
    //     DocumentByRegexSplitter splitter = new DocumentByRegexSplitter(titleRegex, "\n### ", 1024, 50);
    //     List<TextSegment> textSegments = splitter.split(documents.get(0));
    //     System.out.println(textSegments.size());
    //     for (TextSegment segment : textSegments) {
    //         Embedding embedding = embeddingModel.embed(segment).content();
    //         embeddingStore.add(embedding, segment);
    //         System.out.println(segment.text());
    //         System.out.println("------------------------------------------------------");
    //     }
    //
    //     System.out.println(documents.size());
    // }

    /**
     * 知识库查询
     * @param query 查询内容
     * @param maxResults 最大结果数
     * @param minScore 最小相似度分数
     * @return 查询结果
     */
    public QueryResult queryKnowledgeBase(String query, int maxResults, double minScore) {
        try {
            // 将查询向量化
            Embedding queryEmbedding = embeddingModel.embed(query).content();

            // 构建搜索请求
            EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
                    .queryEmbedding(queryEmbedding)
                    .maxResults(maxResults)
                    .minScore(minScore)
                    .build();

            // 执行搜索
            List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(searchRequest).matches();

            if (matches.isEmpty()) {
                return new QueryResult(true, "查询成功", query, "未查询到内容", new ArrayList<>());
            }
            // 构建上下文
            StringBuilder context = new StringBuilder();
            List<MatchResult> matchResults = new ArrayList<>();

            for (EmbeddingMatch<TextSegment> match : matches) {
                context.append(match.embedded().text()).append("\n\n");
                matchResults.add(new MatchResult(match.embedded().text(), match.score()));
            }

            // 使用LLM生成回答
            // String prompt = String.format(
            //     "基于以下上下文信息回答用户问题。如果上下文中没有相关信息，请说明无法找到相关信息。\n\n" +
            //     "上下文：\n%s\n\n" +
            //     "用户问题：%s\n\n" +
            //     "请提供准确、有用的回答：",
            //     context.toString(), query
            // );

            // String answer = chatModel.chat(prompt);

            return new QueryResult(true, "查询成功", query, matches.get(0).embedded().text(), matchResults);
        } catch (Exception e) {
            return new QueryResult(false, "查询失败: " + e.getMessage(), query, null, null);
        }
    }

    /**
     * 向量化文档2
     */
    // public static void main(String[] args) {
    //     String chromaEndpoint = "http://localhost:8000";
    //     EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    //             .builder()
    //             .baseUrl(chromaEndpoint)
    //             .collectionName("test1_collection")
    //             .logRequests(true)
    //             .logResponses(true)
    //             .build();
    //
    //     String aliAiKey = System.getenv("ALI_AI_KEY");
    //     QwenEmbeddingModel embeddingModel = QwenEmbeddingModel.builder()
    //             .apiKey(aliAiKey)
    //             .modelName("text-embedding-v4")
    //             .dimension(1024)
    //             .build();
    //
    //     // List<Document> documents = FileSystemDocumentLoader.loadDocuments("/Users/<USER>/Documents/rag");
    //     Document document = FileSystemDocumentLoader.loadDocument("/Users/<USER>/Documents/rag/本地生活FAQ.md");
    //     // 递归分割
    //     // DocumentSplitter splitter = DocumentSplitters.recursive(1024, 50);
    //     // 按固定字符数（如每 100 字符）分割。
    //     // DocumentByCharacterSplitter splitter = new DocumentByCharacterSplitter(1024, 50);
    //     //  按空格或标点分割单词。
    //     // DocumentByWordSplitter splitter = new DocumentByWordSplitter(1024, 50);
    //     // DocumentBySentenceSplitter splitter = new DocumentBySentenceSplitter(1024, 50);
    //     // 按换行符分割
    //     // DocumentByLineSplitter splitter = new DocumentByLineSplitter(1024, 50);
    //     // 以连续换行符分割
    //     // DocumentByParagraphSplitter splitter = new DocumentByParagraphSplitter(1024, 50);
    //     // 正则分割
    //     String titleRegex = "(?=\\r?\\n#{3}\\s+)";
    //     DocumentByRegexSplitter splitter = new DocumentByRegexSplitter(titleRegex, "\n### ", 1024, 50);
    //     List<TextSegment> textSegments = splitter.split(document);
    //     System.out.println(textSegments.size());
    //     for (TextSegment segment : textSegments) {
    //         // Embedding embedding = embeddingModel.embed(segment).content();
    //         // embeddingStore.add(embedding, segment);
    //         System.out.println(segment.text());
    //         System.out.println("------------------------------------------------------");
    //     }
    //
    // }


}
