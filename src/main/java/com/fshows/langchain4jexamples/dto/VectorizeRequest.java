package com.fshows.langchain4jexamples.dto;

/**
 * 向量化请求DTO
 * <AUTHOR>
 */
public class VectorizeRequest {
    
    /**
     * 文件路径
     */
    private String filePath;
    
    /**
     * 分块大小，默认1024
     */
    private int chunkSize = 1024;
    
    /**
     * 分块重叠，默认50
     */
    private int chunkOverlap = 50;
    
    public VectorizeRequest() {}
    
    public VectorizeRequest(String filePath, int chunkSize, int chunkOverlap) {
        this.filePath = filePath;
        this.chunkSize = chunkSize;
        this.chunkOverlap = chunkOverlap;
    }
    
    // Getters and Setters
    public String getFilePath() {
        return filePath;
    }
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    public int getChunkSize() {
        return chunkSize;
    }
    
    public void setChunkSize(int chunkSize) {
        this.chunkSize = chunkSize;
    }
    
    public int getChunkOverlap() {
        return chunkOverlap;
    }
    
    public void setChunkOverlap(int chunkOverlap) {
        this.chunkOverlap = chunkOverlap;
    }
    
    @Override
    public String toString() {
        return "VectorizeRequest{" +
                "filePath='" + filePath + '\'' +
                ", chunkSize=" + chunkSize +
                ", chunkOverlap=" + chunkOverlap +
                '}';
    }
}
