package com.fshows.langchain4jexamples.dto;

/**
 * 查询请求DTO
 * <AUTHOR>
 */
public class QueryRequest {
    
    /**
     * 查询内容
     */
    private String query;
    
    /**
     * 最大结果数，默认5
     */
    private int maxResults = 5;
    
    /**
     * 最小相似度分数，默认0.7
     */
    private double minScore = 0.7;
    
    public QueryRequest() {}
    
    public QueryRequest(String query, int maxResults, double minScore) {
        this.query = query;
        this.maxResults = maxResults;
        this.minScore = minScore;
    }
    
    // Getters and Setters
    public String getQuery() {
        return query;
    }
    
    public void setQuery(String query) {
        this.query = query;
    }
    
    public int getMaxResults() {
        return maxResults;
    }
    
    public void setMaxResults(int maxResults) {
        this.maxResults = maxResults;
    }
    
    public double getMinScore() {
        return minScore;
    }
    
    public void setMinScore(double minScore) {
        this.minScore = minScore;
    }
    
    @Override
    public String toString() {
        return "QueryRequest{" +
                "query='" + query + '\'' +
                ", maxResults=" + maxResults +
                ", minScore=" + minScore +
                '}';
    }
}
