package com.fshows.langchain4jexamples;

import com.fshows.langchain4jexamples.agent.RagAgent;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * RAG Agent 测试类
 * 注意：运行测试前需要确保：
 * 1. Chroma 数据库已启动 (docker run -p 8000:8000 chromadb/chroma:latest)
 * 2. 设置环境变量 ALI_AI_KEY
 * 3. 准备测试文档文件
 */
@SpringBootTest
@TestPropertySource(properties = {
    "rag.chroma.endpoint=http://localhost:8000",
    "rag.chroma.collection=test_collection"
})
public class RagAgentTest {

    @Autowired
    private RagAgent ragAgent;

    /**
     * 测试文档向量化功能
     * 需要准备一个测试文档文件
     */
    @Test
    public void testVectorizeFile() {
        // 注意：请将此路径替换为实际存在的文档文件路径
        String testFilePath = "/Users/<USER>/Documents/rag/test_document.md";
        
        try {
            RagAgent.VectorizeResult result = ragAgent.vectorizeFile(testFilePath, 512, 25);
            
            System.out.println("向量化结果:");
            System.out.println("成功: " + result.isSuccess());
            System.out.println("消息: " + result.getMessage());
            System.out.println("分段数量: " + result.getSegmentCount());
            
            if (result.isSuccess()) {
                System.out.println("向量化成功！生成了 " + result.getSegmentCount() + " 个文档片段");
            } else {
                System.err.println("向量化失败: " + result.getMessage());
            }
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试知识库查询功能
     * 需要先执行向量化操作，确保知识库中有数据
     */
    @Test
    public void testQueryKnowledgeBase() {
        String query = "什么是人工智能？";
        
        try {
            RagAgent.QueryResult result = ragAgent.queryKnowledgeBase(query, 3, 0.6);
            
            System.out.println("查询结果:");
            System.out.println("成功: " + result.isSuccess());
            System.out.println("消息: " + result.getMessage());
            System.out.println("问题: " + result.getQuery());
            System.out.println("回答: " + result.getAnswer());
            
            if (result.getMatches() != null && !result.getMatches().isEmpty()) {
                System.out.println("\n相关文档片段:");
                for (int i = 0; i < result.getMatches().size(); i++) {
                    RagAgent.MatchResult match = result.getMatches().get(i);
                    System.out.println("片段 " + (i + 1) + " (相似度: " + 
                        String.format("%.3f", match.getScore()) + "):");
                    System.out.println(match.getText().substring(0, 
                        Math.min(200, match.getText().length())) + "...");
                    System.out.println();
                }
            }
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 集成测试：先向量化文档，然后查询
     */
    @Test
    public void testFullWorkflow() {
        // 注意：请将此路径替换为实际存在的文档文件路径
        String testFilePath = "/Users/<USER>/Documents/rag/test_document.md";
        
        System.out.println("=== 开始集成测试 ===");
        
        // 1. 向量化文档
        System.out.println("1. 向量化文档...");
        RagAgent.VectorizeResult vectorizeResult = ragAgent.vectorizeFile(testFilePath, 1024, 50);
        
        if (!vectorizeResult.isSuccess()) {
            System.err.println("向量化失败，跳过查询测试: " + vectorizeResult.getMessage());
            return;
        }
        
        System.out.println("向量化成功，生成 " + vectorizeResult.getSegmentCount() + " 个片段");
        
        // 2. 等待一下确保数据已存储
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 3. 查询知识库
        System.out.println("\n2. 查询知识库...");
        String[] testQueries = {
            "这个文档的主要内容是什么？",
            "有什么重要的概念或定义？",
            "文档中提到了哪些关键信息？"
        };
        
        for (String query : testQueries) {
            System.out.println("\n查询: " + query);
            RagAgent.QueryResult queryResult = ragAgent.queryKnowledgeBase(query, 2, 0.7);
            
            if (queryResult.isSuccess()) {
                System.out.println("回答: " + queryResult.getAnswer());
            } else {
                System.err.println("查询失败: " + queryResult.getMessage());
            }
        }
        
        System.out.println("\n=== 集成测试完成 ===");
    }
}
