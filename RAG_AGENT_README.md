# RAG Agent 使用指南

## 概述

本项目实现了一个基于 LangChain4j 的 RAG (Retrieval-Augmented Generation) 智能问答系统，使用 Chroma 作为向量数据库，Qwen 作为大语言模型。

## 功能特性

- **文档向量化**: 将文档分块并转换为向量存储到 Chroma 数据库
- **智能问答**: 基于向量检索和 Qwen 大模型的智能问答
- **REST API**: 提供标准的 HTTP 接口
- **灵活配置**: 支持自定义分块大小、相似度阈值等参数

## 系统架构

```
用户请求 → REST Controller → RAG Service → Chroma Vector DB
                                    ↓
                              Qwen LLM → 生成回答
```

## 环境要求

1. **Java 17+**
2. **Chroma 向量数据库**
3. **阿里云 DashScope API Key**

## 快速开始

### 1. 启动 Chroma 数据库

```bash
# 使用 Docker 启动 Chroma
docker run -p 8000:8000 chromadb/chroma:latest
```

### 2. 配置环境变量

```bash
export ALI_AI_KEY=your_dashscope_api_key
```

### 3. 启动应用

```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动。

## API 接口

### 1. 向量化文件接口

**POST** `/api/rag/vectorize`

将指定文件进行向量化处理并存储到知识库。

**请求体:**
```json
{
  "filePath": "/path/to/your/document.txt",
  "chunkSize": 1024,
  "chunkOverlap": 50
}
```

**参数说明:**
- `filePath`: 要向量化的文件路径（必填）
- `chunkSize`: 文档分块大小，默认 1024
- `chunkOverlap`: 分块重叠大小，默认 50

**响应示例:**
```json
{
  "success": true,
  "message": "文件向量化成功",
  "segmentCount": 15,
  "segmentIds": ["id1", "id2", "..."]
}
```

### 2. 知识库查询接口

**POST** `/api/rag/query`

基于知识库内容回答用户问题。

**请求体:**
```json
{
  "query": "什么是人工智能？",
  "maxResults": 5,
  "minScore": 0.7
}
```

**参数说明:**
- `query`: 用户问题（必填）
- `maxResults`: 最大检索结果数，默认 5
- `minScore`: 最小相似度分数，默认 0.7

**响应示例:**
```json
{
  "success": true,
  "message": "查询成功",
  "query": "什么是人工智能？",
  "answer": "人工智能是计算机科学的一个分支...",
  "matches": [
    {
      "text": "相关文档片段内容...",
      "score": 0.85
    }
  ]
}
```

### 3. 健康检查接口

**GET** `/api/rag/health`

检查服务运行状态。

**响应:**
```
RAG服务运行正常
```

## 使用示例

### 1. 向量化文档

```bash
curl -X POST http://localhost:8080/api/rag/vectorize \
  -H "Content-Type: application/json" \
  -d '{
    "filePath": "/Users/<USER>/Documents/rag/knowledge.md",
    "chunkSize": 1024,
    "chunkOverlap": 50
  }'
```

### 2. 查询知识库

```bash
curl -X POST http://localhost:8080/api/rag/query \
  -H "Content-Type: application/json" \
  -d '{
    "query": "如何解决团小贝无法使用的问题？",
    "maxResults": 3,
    "minScore": 0.8
  }'
```

## 配置说明

在 `application.properties` 中可以配置以下参数：

```properties
# Chroma 数据库配置
rag.chroma.endpoint=http://localhost:8000
rag.chroma.collection=rag_knowledge_base

# Qwen 模型配置
rag.qwen.model=text-embedding-v4
rag.qwen.dimension=1024
rag.qwen.chat.model=qwen-max
```

## 支持的文件格式

- 文本文件 (.txt)
- Markdown 文件 (.md)
- 其他 LangChain4j 支持的文档格式

## 注意事项

1. 确保 Chroma 数据库正常运行
2. 确保 ALI_AI_KEY 环境变量已正确设置
3. 文件路径必须是服务器可访问的绝对路径
4. 建议根据文档类型调整 chunkSize 和 chunkOverlap 参数

## 错误处理

系统会返回详细的错误信息，常见错误：

- `文件路径不能为空`: 请检查 filePath 参数
- `查询内容不能为空`: 请检查 query 参数
- `文件向量化失败`: 检查文件路径和权限
- `查询失败`: 检查 Chroma 数据库连接

## 扩展功能

可以根据需要扩展以下功能：

1. 支持批量文件向量化
2. 添加文档管理接口（删除、更新）
3. 支持多种文档格式
4. 添加用户认证和权限控制
5. 实现对话历史记录
